import { Injectable, BadRequestException, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import {
  PaymentGateway,
  PaymentMethod,
  PaymentStatus,
  PaymentResult,
  RefundResult,
  StripePaymentParams,
  RazorpayPaymentParams,
  RefundParams,
  PaymentGatewayConfig,
} from "@shared/types";
import { IPaymentService } from "./interfaces/payment-gateway.interface";
import { PaymentGatewayFactory } from "./factories/payment-gateway.factory";

// Legacy interfaces for backward compatibility
interface LegacyStripePaymentParams {
  amount: number;
  currency: string;
  description: string;
  paymentMethodId: string;
  customerId?: string;
  metadata?: Record<string, unknown>;
}

interface LegacyUpiPaymentParams {
  amount: number;
  vpa: string;
  description: string;
  reference: string;
  metadata: Record<string, unknown>;
}

interface LegacyRazorpayPaymentParams {
  amount: number;
  currency: string;
  description?: string;
  customerEmail: string;
  customerPhone?: string;
  metadata?: Record<string, unknown>;
  paymentId?: string;
  orderId?: string;
}

interface LegacyPaymentResult {
  success: boolean;
  transactionId?: string;
  status: PaymentStatus;
  message?: string;
  paymentMethod: PaymentMethod;
  gatewayResponse?: unknown;
}

export interface PaymentMethodDto {
  id: string;
  type: string;
  details: any;
  isDefault: boolean;
  createdAt: string;
  updatedAt?: string;
}

export interface PaymentSummary {
  totalSpent: number;
  totalPayments: number;
  pendingPayments: number;
  completedPayments: number;
  failedPayments: number;
  lastPaymentDate: string;
}

export interface PaymentListResponse {
  payments: any[];
  total: number;
  limit: number;
  offset: number;
}

@Injectable()
export class PaymentsService implements IPaymentService {
  private readonly logger = new Logger(PaymentsService.name);

  constructor(
    private configService: ConfigService,
    private paymentGatewayFactory: PaymentGatewayFactory
  ) {}

  /**
   * Process payment using the best available gateway
   */
  async processPayment(
    params: StripePaymentParams | RazorpayPaymentParams,
    preferredGateway?: PaymentGateway
  ): Promise<PaymentResult> {
    try {
      const gateway = preferredGateway || this.selectBestGateway(params);
      const gatewayInstance = this.paymentGatewayFactory.createGateway(gateway);

      return await gatewayInstance.processPayment(params);
    } catch (error) {
      this.logger.error("Payment processing failed:", error);
      throw new BadRequestException(
        `Payment processing failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Create payment intent using the best available gateway
   */
  async createPaymentIntent(
    params: StripePaymentParams | RazorpayPaymentParams,
    preferredGateway?: PaymentGateway
  ): Promise<PaymentResult> {
    try {
      const gateway = preferredGateway || this.selectBestGateway(params);
      const gatewayInstance = this.paymentGatewayFactory.createGateway(gateway);

      return await gatewayInstance.createPaymentIntent(params);
    } catch (error) {
      this.logger.error("Payment intent creation failed:", error);
      throw new BadRequestException(
        `Payment intent creation failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Refund payment using the original gateway
   */
  async refundPayment(params: RefundParams): Promise<RefundResult> {
    try {
      // Determine which gateway was used for the original payment
      const gateway = await this.determineGatewayFromTransaction(
        params.transactionId
      );
      const gatewayInstance = this.paymentGatewayFactory.createGateway(gateway);

      return await gatewayInstance.refundPayment(params);
    } catch (error) {
      this.logger.error("Refund processing failed:", error);
      throw new BadRequestException(
        `Refund processing failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Get available payment methods for a currency
   */
  async getAvailablePaymentMethods(currency: string): Promise<string[]> {
    return this.paymentGatewayFactory.getAvailablePaymentMethodsForCurrency(
      currency
    );
  }

  /**
   * Get gateway configurations
   */
  async getGatewayConfigurations(): Promise<PaymentGatewayConfig[]> {
    return this.paymentGatewayFactory.getGatewayConfigurations();
  }

  /**
   * Helper method to select the best gateway for payment parameters
   */
  private selectBestGateway(
    params: StripePaymentParams | RazorpayPaymentParams
  ): PaymentGateway {
    const currency = params.currency.toUpperCase();

    // Use the factory's logic to determine the best gateway
    return this.paymentGatewayFactory.getDefaultGateway(currency);
  }

  /**
   * Helper method to determine which gateway was used for a transaction
   */
  private async determineGatewayFromTransaction(
    transactionId: string
  ): Promise<PaymentGateway> {
    // Stripe payment intent IDs start with 'pi_'
    if (transactionId.startsWith("pi_")) {
      return PaymentGateway.STRIPE;
    }

    // Razorpay order IDs start with 'order_' and payment IDs start with 'pay_'
    if (
      transactionId.startsWith("order_") ||
      transactionId.startsWith("pay_")
    ) {
      return PaymentGateway.RAZORPAY;
    }

    // Default to Razorpay for unknown formats (could be enhanced with database lookup)
    this.logger.warn(
      `Unknown transaction ID format: ${transactionId}, defaulting to Razorpay`
    );
    return PaymentGateway.RAZORPAY;
  }

  /**
   * Legacy method - Process a payment using Stripe
   * @deprecated Use processPayment with PaymentGateway.STRIPE instead
   */
  async processStripePayment(
    params: LegacyStripePaymentParams
  ): Promise<LegacyPaymentResult> {
    const stripeParams: StripePaymentParams = {
      amount: params.amount,
      currency: params.currency,
      description: params.description,
      paymentMethodId: params.paymentMethodId,
      ...(params.customerId && { customerId: params.customerId }),
      ...(params.metadata && { metadata: params.metadata }),
    };

    const result = await this.processPayment(
      stripeParams,
      PaymentGateway.STRIPE
    );

    return {
      success: result.success,
      ...(result.transactionId && { transactionId: result.transactionId }),
      status: result.status,
      ...(result.message && { message: result.message }),
      paymentMethod: result.paymentMethod,
      ...(result.gatewayResponse && {
        gatewayResponse: result.gatewayResponse,
      }),
    };
  }

  /**
   * Legacy method - Process a payment using UPI
   * @deprecated UPI payments should be handled through the gateway system
   */
  async processUpiPayment(
    params: LegacyUpiPaymentParams
  ): Promise<LegacyPaymentResult> {
    // UPI can be processed through either Razorpay or a dedicated UPI gateway
    // For now, we'll use Razorpay as it supports UPI
    const razorpayParams: RazorpayPaymentParams = {
      amount: params.amount,
      currency: "INR", // UPI is typically INR
      description: params.description,
      metadata: {
        ...params.metadata,
        vpa: params.vpa,
        reference: params.reference,
      },
    };

    const result = await this.processPayment(
      razorpayParams,
      PaymentGateway.RAZORPAY
    );

    return {
      success: result.success,
      ...(result.transactionId && { transactionId: result.transactionId }),
      status: result.status,
      ...(result.message && { message: result.message }),
      paymentMethod: PaymentMethod.UPI,
      ...(result.gatewayResponse && {
        gatewayResponse: result.gatewayResponse,
      }),
    };
  }

  /**
   * Legacy method - Create a Razorpay order for payment
   * @deprecated Use createPaymentIntent with PaymentGateway.RAZORPAY instead
   */
  async createRazorpayOrder(
    params: LegacyRazorpayPaymentParams
  ): Promise<LegacyPaymentResult> {
    const razorpayParams: RazorpayPaymentParams = {
      amount: params.amount,
      currency: params.currency,
      customerEmail: params.customerEmail,
      ...(params.description && { description: params.description }),
      ...(params.customerPhone && { customerPhone: params.customerPhone }),
      ...(params.metadata && { metadata: params.metadata }),
      ...(params.orderId && { orderId: params.orderId }),
    };

    const result = await this.createPaymentIntent(
      razorpayParams,
      PaymentGateway.RAZORPAY
    );

    return {
      success: result.success,
      ...(result.transactionId && { transactionId: result.transactionId }),
      status: result.status,
      ...(result.message && { message: result.message }),
      paymentMethod: result.paymentMethod,
      ...(result.gatewayResponse && {
        gatewayResponse: result.gatewayResponse,
      }),
    };
  }

  /**
   * Legacy method - Process a payment using Razorpay
   * @deprecated Use processPayment with PaymentGateway.RAZORPAY instead
   */
  async processRazorpayPayment(
    params: LegacyRazorpayPaymentParams
  ): Promise<LegacyPaymentResult> {
    const razorpayParams: RazorpayPaymentParams = {
      amount: params.amount,
      currency: params.currency,
      customerEmail: params.customerEmail,
      ...(params.description && { description: params.description }),
      ...(params.customerPhone && { customerPhone: params.customerPhone }),
      ...(params.metadata && { metadata: params.metadata }),
      ...(params.orderId && { orderId: params.orderId }),
    };

    const result = await this.processPayment(
      razorpayParams,
      PaymentGateway.RAZORPAY
    );

    return {
      success: result.success,
      ...(result.transactionId && { transactionId: result.transactionId }),
      status: result.status,
      ...(result.message && { message: result.message }),
      paymentMethod: result.paymentMethod,
      ...(result.gatewayResponse && {
        gatewayResponse: result.gatewayResponse,
      }),
    };
  }

  /**
   * Legacy method - Capture a Razorpay payment
   * @deprecated Use gateway-specific capture methods instead
   */
  async captureRazorpayPayment(
    paymentId: string,
    amount: number
  ): Promise<LegacyPaymentResult> {
    try {
      const gateway = this.paymentGatewayFactory.createGateway(
        PaymentGateway.RAZORPAY
      );
      const result = await gateway.capturePayment(paymentId, amount);

      return {
        success: result.success,
        ...(result.transactionId && { transactionId: result.transactionId }),
        status: result.status,
        ...(result.message && { message: result.message }),
        paymentMethod: result.paymentMethod,
        ...(result.gatewayResponse && {
          gatewayResponse: result.gatewayResponse,
        }),
      };
    } catch (error) {
      this.logger.error("Razorpay payment capture failed:", error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        message: error instanceof Error ? error.message : "Unknown error",
        paymentMethod: PaymentMethod.RAZORPAY,
        gatewayResponse: error,
      };
    }
  }

  /**
   * Legacy method - Refund a Razorpay payment
   * @deprecated Use refundPayment method instead
   */
  async refundRazorpayPayment(
    paymentId: string,
    amount?: number,
    notes?: Record<string, unknown>
  ): Promise<LegacyPaymentResult> {
    try {
      const refundParams: RefundParams = {
        transactionId: paymentId,
        ...(amount && { amount }),
        ...(notes && { metadata: notes }),
      };

      const result = await this.refundPayment(refundParams);

      return {
        success: result.success,
        transactionId: result.refundId,
        status:
          result.status === "succeeded"
            ? PaymentStatus.REFUNDED
            : PaymentStatus.FAILED,
        message: "Refund processed",
        paymentMethod: PaymentMethod.RAZORPAY,
        ...(result.gatewayResponse && {
          gatewayResponse: result.gatewayResponse,
        }),
      };
    } catch (error) {
      this.logger.error("Razorpay refund failed:", error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        message: error instanceof Error ? error.message : "Unknown error",
        paymentMethod: PaymentMethod.RAZORPAY,
        gatewayResponse: error,
      };
    }
  }

  /**
   * Legacy method - Verify Razorpay payment signature
   * @deprecated Use gateway-specific verification methods instead
   */
  verifyRazorpaySignature(
    orderId: string,
    paymentId: string,
    signature: string
  ): boolean {
    try {
      const gateway = this.paymentGatewayFactory.createGateway(
        PaymentGateway.RAZORPAY
      );
      const webhookSecret = this.configService.get<string>(
        "RAZORPAY_WEBHOOK_SECRET",
        ""
      );
      const body = orderId + "|" + paymentId;

      return gateway.verifyWebhookSignature(body, signature, webhookSecret);
    } catch (error) {
      this.logger.error("Razorpay signature verification failed:", error);
      return false;
    }
  }
}
